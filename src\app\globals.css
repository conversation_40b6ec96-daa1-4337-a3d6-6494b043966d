@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&family=Almarai:wght@300;400;700;800&display=swap');

@import "tailwindcss";

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 14.9020%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 14.9020%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 14.9020%);
  --primary: hsl(37.6923 92.1260% 50.1961%);
  --primary-foreground: hsl(0 0% 0%);
  --secondary: hsl(220.0000 14.2857% 95.8824%);
  --secondary-foreground: hsl(215 13.7931% 34.1176%);
  --muted: hsl(210 20.0000% 98.0392%);
  --muted-foreground: hsl(220 8.9362% 46.0784%);
  --accent: hsl(48.0000 100.0000% 96.0784%);
  --accent-foreground: hsl(22.7273 82.5000% 31.3725%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(220 13.0435% 90.9804%);
  --input: hsl(220 13.0435% 90.9804%);
  --ring: hsl(37.6923 92.1260% 50.1961%);
  --chart-1: hsl(37.6923 92.1260% 50.1961%);
  --chart-2: hsl(32.1327 94.6188% 43.7255%);
  --chart-3: hsl(25.9649 90.4762% 37.0588%);
  --chart-4: hsl(22.7273 82.5000% 31.3725%);
  --chart-5: hsl(21.7143 77.7778% 26.4706%);
  --sidebar: hsl(210 20.0000% 98.0392%);
  --sidebar-foreground: hsl(0 0% 14.9020%);
  --sidebar-primary: hsl(37.6923 92.1260% 50.1961%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(48.0000 100.0000% 96.0784%);
  --sidebar-accent-foreground: hsl(22.7273 82.5000% 31.3725%);
  --sidebar-border: hsl(220 13.0435% 90.9804%);
  --sidebar-ring: hsl(37.6923 92.1260% 50.1961%);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(0 0% 9.0196%);
  --foreground: hsl(0 0% 89.8039%);
  --card: hsl(0 0% 14.9020%);
  --card-foreground: hsl(0 0% 89.8039%);
  --popover: hsl(0 0% 14.9020%);
  --popover-foreground: hsl(0 0% 89.8039%);
  --primary: hsl(37.6923 92.1260% 50.1961%);
  --primary-foreground: hsl(0 0% 0%);
  --secondary: hsl(0 0% 14.9020%);
  --secondary-foreground: hsl(0 0% 89.8039%);
  --muted: hsl(0 0% 14.9020%);
  --muted-foreground: hsl(0 0% 63.9216%);
  --accent: hsl(22.7273 82.5000% 31.3725%);
  --accent-foreground: hsl(48 96.6387% 76.6667%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 25.0980%);
  --input: hsl(0 0% 25.0980%);
  --ring: hsl(37.6923 92.1260% 50.1961%);
  --chart-1: hsl(43.2558 96.4126% 56.2745%);
  --chart-2: hsl(32.1327 94.6188% 43.7255%);
  --chart-3: hsl(22.7273 82.5000% 31.3725%);
  --chart-4: hsl(25.9649 90.4762% 37.0588%);
  --chart-5: hsl(22.7273 82.5000% 31.3725%);
  --sidebar: hsl(0 0% 5.8824%);
  --sidebar-foreground: hsl(0 0% 89.8039%);
  --sidebar-primary: hsl(37.6923 92.1260% 50.1961%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(22.7273 82.5000% 31.3725%);
  --sidebar-accent-foreground: hsl(48 96.6387% 76.6667%);
  --sidebar-border: hsl(0 0% 25.0980%);
  --sidebar-ring: hsl(37.6923 92.1260% 50.1961%);
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* RTL Support */
html {
  direction: rtl;
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-arabic);
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

/* Premium scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: color-mix(in srgb, var(--color-muted) 30%, transparent);
}

::-webkit-scrollbar-thumb {
  background-color: color-mix(in srgb, var(--color-primary) 40%, transparent);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: color-mix(in srgb, var(--color-primary) 60%, transparent);
}

/* Premium Glassmorphism Effect */
.glass-effect {
  background: var(--gradient-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid color-mix(in srgb, var(--color-border) 20%, transparent);
}

/* Premium Card Styles */
.premium-card {
  background-color: var(--color-card);
  border: 1px solid color-mix(in srgb, var(--color-border) 50%, transparent);
  border-radius: calc(var(--radius) + 4px);
  box-shadow: var(--shadow-soft);
  background: linear-gradient(135deg, var(--color-card) 0%, color-mix(in srgb, var(--color-card) 95%, transparent) 100%);
}

.premium-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Premium Button Variants */
.btn-premium {
  position: relative;
  overflow: hidden;
  background: var(--gradient-primary);
  box-shadow: var(--shadow-soft);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .premium-card {
    border-radius: var(--radius);
    border: 0;
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--color-card) 0%, color-mix(in srgb, var(--color-card) 98%, transparent) 100%);
  }

  .glass-effect {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  /* Mobile app-like layout */
  .mobile-app-layout {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--color-background), var(--color-background), color-mix(in srgb, var(--color-muted) 10%, transparent));
  }

  /* Mobile sidebar improvements */
  .sidebar-mobile {
    width: 100%;
    max-width: var(--max-width-xs);
    box-shadow: var(--shadow-2xl);
  }

  /* Mobile typography */
  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.25;
  }

  .mobile-text-base {
    font-size: 1rem;
    line-height: 1.375;
  }

  /* Mobile card spacing */
  .mobile-card-spacing {
    padding: 1rem;
    gap: 1rem;
  }

  /* Mobile button improvements */
  .mobile-btn {
    height: 3rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    border-radius: calc(var(--radius) + 4px);
    font-weight: 500;
  }

  /* Safe area for mobile devices */
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
  }

  /* Mobile App Layout */
  .mobile-app-layout {
    -webkit-font-smoothing: antialiased;
    color: var(--color-foreground);
    width: 100%;
  }

  /* Mobile Sidebar */
  .sidebar-mobile {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 50;
    width: 20rem;
    transform: translateX(0);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  @media (min-width: 768px) {
    .sidebar-mobile {
      position: relative;
      transform: translateX(0);
    }
  }

  .sidebar-mobile[data-state="collapsed"] {
    transform: translateX(-100%);
  }

  .sidebar-mobile[data-state="expanded"] {
    transform: translateX(0);
  }
}

/* Dark mode improvements */
.dark .premium-card {
  background: linear-gradient(135deg, var(--color-card) 0%, color-mix(in srgb, var(--color-card) 98%, transparent) 100%);
  border-color: color-mix(in srgb, var(--color-border) 30%, transparent);
}

.dark .glass-effect {
  background: linear-gradient(135deg, oklch(0.7058 0.0777 302.0489 / 0.08) 0%, oklch(0.6058 0.0777 302.0489 / 0.04) 100%);
  border-color: color-mix(in srgb, var(--color-border) 10%, transparent);
}

/* Touch improvements for mobile */
@media (hover: none) and (pointer: coarse) {
  .hover-scale {
    transform: none;
  }

  .premium-card:hover {
    transform: none;
  }

  /* Larger touch targets on mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Premium Input Styles */
.input-premium {
  background-color: var(--color-card);
  border: 1px solid color-mix(in srgb, var(--color-border) 50%, transparent);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-premium:focus {
  border-color: color-mix(in srgb, var(--color-primary) 50%, transparent);
  outline: 2px solid color-mix(in srgb, var(--color-primary) 20%, transparent);
  outline-offset: 2px;
  box-shadow: var(--shadow-glow);
}

/* Arabic Typography Classes */
.heading-arabic {
  font-family: var(--font-almarai);
  font-weight: 700;
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px color-mix(in srgb, var(--color-foreground) 10%, transparent);
}

.text-arabic {
  font-family: var(--font-arabic);
  line-height: 1.625;
}

/* Premium Animations */
.fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-in-right {
  animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Base styles */
* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
}


#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}